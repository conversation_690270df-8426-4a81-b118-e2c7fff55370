import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();
import { User } from '../../users/entities/user.entity';
import { Category } from '../../categories/entities/category.entity';
import { Product } from '../../products/entities/product.entity';
import { ProductImage } from '../../products/entities/product-image.entity';
import { Post } from '../../microblog/entities/post.entity';
import { Comment } from '../../microblog/entities/comment.entity';
import { Like } from '../../microblog/entities/like.entity';
import { Follower } from '../../microblog/entities/follower.entity';
import { Notification } from '../../notifications/entities/notification.entity';
import { seedDatabase } from './seed';

async function runSeed() {
  const configService = new ConfigService();
  
  const dataSource = new DataSource({
    type: 'postgres',
    host: configService.get('DATABASE_HOST', 'localhost'),
    port: configService.get('DATABASE_PORT', 5432),
    username: configService.get('DATABASE_USERNAME', 'postgres'),
    password: configService.get('DATABASE_PASSWORD', 'AU110s/6081/2021PG'),
    database: configService.get('DATABASE_NAME', 'postgres'),
    entities: [
      User,
      Category,
      Product,
      ProductImage,
      Post,
      Comment,
      Like,
      Follower,
      Notification,
    ],
    synchronize: false,
  });

  try {
    await dataSource.initialize();
    console.log('📦 Database connection established');
    
    await seedDatabase(dataSource);
    
    await dataSource.destroy();
    console.log('✅ Seeding completed and connection closed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    process.exit(1);
  }
}

runSeed();
