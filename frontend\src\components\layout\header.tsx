// src/components/Header.tsx
'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ModeToggle } from '@/components/mode-toggle';
import { Search, ShoppingCart, Menu } from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { MainNav } from './MainNav';
import { MobileNav } from './MobileNav';


export default function Header() {
  const { isAuthenticated } = useAuthStore();

  return (
    <header className="border-b bg-background/95 backdrop-blur-sm">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        {/* Logo */}
        <Link href="/" className="text-2xl font-bold text-market-blue">
          Market O'Clock
        </Link>

        {/* Navigation */}
        <nav className="hidden md:flex items-center gap-6">
          <Link href="/marketplace" className="text-sm font-medium hover:underline">
            Marketplace
          </Link>
          <Link href="/microblog" className="text-sm font-medium hover:underline">
            Market Talk
          </Link>
          <Link href="/blogs" className="text-sm font-medium hover:underline">
            Blogs
          </Link>
          <Link href="/about" className="text-sm font-medium hover:underline">
            About
          </Link>
        </nav>

        {/* Actions */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon">
            <Search className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <ShoppingCart className="h-4 w-4" />
          </Button>
          <ModeToggle />
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-4 w-4" />
          </Button>
          {isAuthenticated ? (
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard">Dashboard</Link>
            </Button>
          ) : (
            <>
              <Button variant="outline" size="sm" asChild>
                <Link href="/login">Login</Link>
              </Button>
              <Button size="sm" className="bg-market-blue hover:bg-market-blue/90" asChild>
                <Link href="/register">Sign Up</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </header>
  );
}