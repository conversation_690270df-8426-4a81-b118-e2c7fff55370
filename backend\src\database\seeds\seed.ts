import { DataSource } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Category } from '../../products/entities/category.entity';
import { Product } from '../../products/entities/product.entity';
import { ProductImage } from '../../products/entities/product-image.entity';
import { Post } from '../../microblog/entities/post.entity';
import * as bcrypt from 'bcryptjs';

export async function seedDatabase(dataSource: DataSource) {
  console.log('🌱 Starting database seeding...');

  const userRepository = dataSource.getRepository(User);
  const categoryRepository = dataSource.getRepository(Category);
  const productRepository = dataSource.getRepository(Product);
  const postRepository = dataSource.getRepository(Post);

  // Clear existing data using TRUNCATE CASCADE
  console.log('🧹 Clearing existing data...');

  // Use raw SQL to truncate tables with CASCADE to handle foreign keys
  await dataSource.query('TRUNCATE TABLE "comments", "likes", "followers", "notifications", "posts", "product_images", "products", "categories", "user" RESTART IDENTITY CASCADE');

  // Create test users
  const hashedPassword = await bcrypt.hash('password123', 10);
  
  const users = await userRepository.save([
    {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      isEmailVerified: true,
    },
    {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'John',
      lastName: 'Supplier',
      role: 'supplier',
      isEmailVerified: true,
    },
    {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Jane',
      lastName: 'Retailer',
      role: 'retailer',
      isEmailVerified: true,
    },
    {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Bob',
      lastName: 'Customer',
      role: 'customer',
      isEmailVerified: true,
    },
  ]);

  console.log('✅ Created test users');

  // Create categories
  const categories = await categoryRepository.save([
    {
      name: 'Electronics',
      description: 'Electronic devices and gadgets',
    },
    {
      name: 'Furniture',
      description: 'Home and office furniture',
    },
    {
      name: 'Kitchen & Dining',
      description: 'Kitchen appliances and dining accessories',
    },
    {
      name: 'Home & Garden',
      description: 'Home improvement and garden supplies',
    },
  ]);

  console.log('✅ Created categories');

  // Create products
  const products = await productRepository.save([
    {
      title: 'Kids Study Table',
      description: 'Perfect study table for children with storage compartments',
      price: 150.00,
      stock: 10,
      category: categories[1], // Furniture
      seller: users[1], // Supplier
      thumbnailUrl: '/images/Kidsstudy-table.jpg',
    },
    {
      title: 'Water Dispenser',
      description: 'Modern water dispenser with hot and cold water options',
      price: 89.99,
      stock: 15,
      category: categories[2], // Kitchen & Dining
      seller: users[1], // Supplier
      thumbnailUrl: '/images/Water-dispenser.jpg',
    },
    {
      title: 'Console Bedside Table',
      description: 'Elegant bedside table with modern design',
      price: 120.00,
      stock: 8,
      category: categories[1], // Furniture
      seller: users[1], // Supplier
      thumbnailUrl: '/images/console-bedside-table-.jpg',
    },
    {
      title: 'Nesting Coffee Table',
      description: 'Space-saving nesting coffee tables set',
      price: 199.99,
      stock: 5,
      category: categories[1], // Furniture
      seller: users[1], // Supplier
      thumbnailUrl: '/images/nesting-coffee-table.jpg',
    },
    {
      title: 'Sutai Slow Juicer',
      description: 'High-quality slow juicer for healthy living',
      price: 299.99,
      stock: 12,
      category: categories[2], // Kitchen & Dining
      seller: users[1], // Supplier
      thumbnailUrl: '/images/sutai-slow-juicer.jpg',
    },
    {
      title: 'Cook Ware Set',
      description: 'Complete cooking set with non-stick coating',
      price: 79.99,
      stock: 20,
      category: categories[2], // Kitchen & Dining
      seller: users[1], // Supplier
      thumbnailUrl: '/images/cook-ware.jpg',
    },
  ]);

  console.log('✅ Created products');

  // Create sample posts
  const posts = await postRepository.save([
    {
      content: 'Welcome to Market O\'Clock! 🎉 We\'re excited to connect suppliers and retailers in this amazing marketplace.',
      user: users[0], // Admin
    },
    {
      content: 'Just added some amazing furniture pieces to our catalog! Check out our new study tables and coffee tables. Perfect for modern homes! 🏠✨',
      user: users[1], // Supplier
    },
    {
      content: 'Looking for quality kitchen appliances for my restaurant. The water dispensers and juicers look promising! 🍹👨‍🍳',
      user: users[2], // Retailer
    },
    {
      content: 'The slow juicer I bought last week is amazing! Fresh juice every morning. Highly recommend! 🥤💚',
      user: users[3], // Customer
    },
  ]);

  console.log('✅ Created sample posts');

  console.log('🎉 Database seeding completed successfully!');
  console.log(`Created ${users.length} users, ${categories.length} categories, ${products.length} products, and ${posts.length} posts`);
}
